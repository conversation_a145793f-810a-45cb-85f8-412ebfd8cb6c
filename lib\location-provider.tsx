"use client"

import React, { createContext, useContext, useState, useEffect, useCallback } from "react"
import { SettingsStorage, Location } from "@/lib/settings-storage"
import { locationEventBus } from "@/lib/location-event-bus"
import { locationCache } from "@/lib/location-cache"
import { LocationMigration } from "@/lib/location-migration"

interface LocationContextType {
  locations: Location[]
  getLocationById: (id: string) => Location | undefined
  getLocationName: (id: string) => string
  getLocationIds: () => string[]
  getActiveLocations: () => Location[]
  refreshLocations: () => void
  isHomeServiceEnabled: boolean
  addLocation: (location: Location) => void
  updateLocation: (location: Location) => void
  deleteLocation: (locationId: string) => void
  syncLocations: () => void
}

const LocationContext = createContext<LocationContextType>({
  locations: [],
  getLocationById: () => undefined,
  getLocationName: () => "Unknown Location",
  getLocationIds: () => [],
  getActiveLocations: () => [],
  refreshLocations: () => {},
  isHomeServiceEnabled: false,
  addLocation: () => {},
  updateLocation: () => {},
  deleteLocation: () => {},
  syncLocations: () => {},
})

export function LocationProvider({ children }: { children: React.ReactNode }) {
  console.log("🚀 LocationProvider: Component initialized")
  const [locations, setLocations] = useState<Location[]>([])
  const [isHomeServiceEnabled, setIsHomeServiceEnabled] = useState(false)

  // Load locations from database API
  const loadLocations = useCallback(async () => {
    try {
      console.log("🔄 LocationProvider: Loading locations from database...")

      // Run migration if needed
      if (LocationMigration.isMigrationNeeded()) {
        console.log("🚀 LocationProvider: Running location data migration...")
        await LocationMigration.runMigration()
      }

      // Fetch locations from database API
      const response = await fetch('/api/locations')
      if (!response.ok) {
        throw new Error(`Failed to fetch locations: ${response.statusText}`)
      }

      const data = await response.json()
      const dbLocations = data.locations || []

      console.log(`✅ LocationProvider: Loaded ${dbLocations.length} locations from database`)

      // Convert database locations to our Location interface format
      const formattedLocations: Location[] = dbLocations.map((loc: any) => ({
        id: loc.id,
        name: loc.name,
        address: loc.address || '',
        city: loc.city || '',
        state: loc.state || '',
        zipCode: loc.zipCode || '',
        country: loc.country || '',
        phone: loc.phone || '',
        email: loc.email || '',
        status: loc.isActive ? 'Active' : 'Inactive',
        description: '',
        enableOnlineBooking: true,
        displayOnWebsite: true,
        staffCount: 0,
        servicesCount: 0,
      }))

      // Set locations state directly from database
      setLocations(formattedLocations)

      // Update location cache with database locations (for backward compatibility)
      locationCache.refreshCache()
      setIsHomeServiceEnabled(true)

      console.log("✅ LocationProvider: Locations loaded successfully")
      console.log("✅ LocationProvider: Set locations state with:", formattedLocations.length, "locations")
      console.log("✅ LocationProvider: Location names:", formattedLocations.map(loc => loc.name))
    } catch (error) {
      console.error("❌ LocationProvider: Error loading locations:", error)
      // Fallback to cache if API fails
      locationCache.initialize()
      const cachedLocations = locationCache.getAllLocations()
      setLocations(cachedLocations)
    }
  }, [])

  // Load locations on mount
  useEffect(() => {
    console.log("🚀 LocationProvider: useEffect triggered - loading locations")

    // Define loadLocations inline to avoid dependency issues
    const loadLocationsInline = async () => {
      try {
        console.log("🔄 LocationProvider: Loading locations from database...")

        // Fetch locations from database API
        const response = await fetch('/api/locations')
        if (!response.ok) {
          throw new Error(`Failed to fetch locations: ${response.statusText}`)
        }

        const data = await response.json()
        const dbLocations = data.locations || []

        console.log(`✅ LocationProvider: Loaded ${dbLocations.length} locations from database`)

        // Convert database locations to our Location interface format
        const formattedLocations: Location[] = dbLocations.map((loc: any) => ({
          id: loc.id,
          name: loc.name,
          address: loc.address || '',
          city: loc.city || '',
          state: loc.state || '',
          zipCode: loc.zipCode || '',
          country: loc.country || '',
          phone: loc.phone || '',
          email: loc.email || '',
          status: loc.isActive ? 'Active' : 'Inactive',
          description: '',
          enableOnlineBooking: true,
          displayOnWebsite: true,
          staffCount: 0,
          servicesCount: 0,
        }))

        // Add Home Service location if enabled and not already present
        const hasHomeService = formattedLocations.some(loc => loc.id === "home" || loc.name === "Home Service")
        if (!hasHomeService) {
          formattedLocations.push({
            id: "home",
            name: "Home Service",
            address: "",
            city: "",
            state: "",
            zipCode: "",
            country: "",
            phone: "",
            email: "",
            status: "Active",
            description: "Home service location for mobile appointments",
            enableOnlineBooking: true,
            displayOnWebsite: true,
            staffCount: 0,
            servicesCount: 0,
          })
        }

        // Set locations state directly from database
        setLocations(formattedLocations)
        setIsHomeServiceEnabled(true)

        console.log("✅ LocationProvider: Locations loaded successfully")
        console.log("✅ LocationProvider: Set locations state with:", formattedLocations.length, "locations")
        console.log("✅ LocationProvider: Location names:", formattedLocations.map(loc => loc.name))
      } catch (error) {
        console.error("❌ LocationProvider: Error loading locations:", error)
        // Set empty array on error
        setLocations([])
      }
    }

    loadLocationsInline()

    // Subscribe to specific location events, excluding 'locations-refreshed'
    // to avoid infinite loops
    const unsubscribeAdded = locationEventBus.subscribe('location-added', () => {
      // Only reload locations when a location is added
      const cachedLocations = locationCache.getAllLocations();
      setLocations(cachedLocations);
      setIsHomeServiceEnabled(true);
    });

    const unsubscribeUpdated = locationEventBus.subscribe('location-updated', () => {
      // Only reload locations when a location is updated
      const cachedLocations = locationCache.getAllLocations();
      setLocations(cachedLocations);
    });

    const unsubscribeRemoved = locationEventBus.subscribe('location-removed', () => {
      // Only reload locations when a location is removed
      const cachedLocations = locationCache.getAllLocations();
      setLocations(cachedLocations);
    });

    const unsubscribeCurrentChanged = locationEventBus.subscribe('current-location-changed', () => {
      // No need to reload locations when current location changes
      // This is handled by the component that changes the current location
    });

    return () => {
      // Unsubscribe when component unmounts
      unsubscribeAdded();
      unsubscribeUpdated();
      unsubscribeRemoved();
      unsubscribeCurrentChanged();
    }
  }, []) // Empty dependency array since we're using inline function

  // Get location by ID (using current state)
  const getLocationById = useCallback((id: string): Location | undefined => {
    return locations.find(location => location.id === id)
  }, [locations])

  // Get location name by ID (using cache)
  const getLocationName = useCallback((id: string): string => {
    return locationCache.getLocationName(id)
  }, [])

  // Get all location IDs
  const getLocationIds = useCallback((): string[] => {
    return locations.map(location => location.id)
  }, [locations])

  // Get active locations
  const getActiveLocations = useCallback((): Location[] => {
    return locations.filter(location => location.status === 'Active')
  }, [locations])

  // Refresh locations from database
  const refreshLocations = useCallback(async () => {
    console.log("🔄 LocationProvider: Refreshing locations from database...")
    await loadLocations()
  }, [loadLocations])

  // Add a new location
  const addLocation = useCallback(async (location: Location) => {
    // Validate location name and prevent reserved names
    if (!location.name || location.name.trim() === "") {
      console.warn("Cannot add a location without a name")
      return
    }

    console.log("🔄 LocationProvider: Adding location to database:", location.name);

    try {
      // Add location to database via API
      const response = await fetch('/api/locations', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          name: location.name,
          address: location.address,
          city: location.city,
          state: location.state,
          zipCode: location.zipCode,
          country: location.country,
          phone: location.phone,
          email: location.email,
        }),
      })

      if (!response.ok) {
        throw new Error(`Failed to add location: ${response.statusText}`)
      }

      const result = await response.json()
      console.log("✅ LocationProvider: Location added to database:", result.location.name)

      // Reload locations from database
      await loadLocations()

      // Publish location-added event
      locationEventBus.publish({
        type: 'location-added',
        payload: location
      })

      console.log("✅ LocationProvider: Location added successfully:", location.name);
    } catch (error) {
      console.error("❌ LocationProvider: Error adding location:", error)
    }
  }, [loadLocations])

  // Update an existing location
  const updateLocation = useCallback(async (location: Location) => {
    // Validate location name and prevent reserved names
    if (!location.name || location.name.trim() === "") {
      console.warn("Cannot update a location without a name")
      return
    }

    console.log("🔄 LocationProvider: Updating location in database:", location.name, location.id);

    try {
      // Update location in database via API
      const response = await fetch(`/api/locations/${location.id}`, {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          name: location.name,
          address: location.address,
          city: location.city,
          state: location.state,
          zipCode: location.zipCode,
          country: location.country,
          phone: location.phone,
          email: location.email,
          isActive: location.status === 'Active',
        }),
      })

      if (!response.ok) {
        throw new Error(`Failed to update location: ${response.statusText}`)
      }

      const result = await response.json()
      console.log("✅ LocationProvider: Location updated in database:", result.location.name)

      // Reload locations from database
      await loadLocations()

      // Publish location-updated event
      locationEventBus.publish({
        type: 'location-updated',
        payload: location
      })

      console.log("✅ LocationProvider: Location updated successfully:", location.name);
    } catch (error) {
      console.error("❌ LocationProvider: Error updating location:", error)
    }
  }, [loadLocations])

  // Delete a location
  const deleteLocation = useCallback(async (locationId: string) => {
    console.log("🔄 LocationProvider: Deleting location from database:", locationId);

    try {
      // Delete location from database via API
      const response = await fetch(`/api/locations/${locationId}`, {
        method: 'DELETE',
      })

      if (!response.ok) {
        throw new Error(`Failed to delete location: ${response.statusText}`)
      }

      console.log("✅ LocationProvider: Location deleted from database:", locationId)

      // Reload locations from database
      await loadLocations()

      // Publish location-removed event
      locationEventBus.publish({
        type: 'location-removed',
        payload: locationId
      })

      console.log("✅ LocationProvider: Location deleted successfully:", locationId);
    } catch (error) {
      console.error("❌ LocationProvider: Error deleting location:", error)
    }
  }, [loadLocations])

  // Synchronize locations across the application
  const syncLocations = useCallback(() => {
    // Refresh the cache
    locationCache.refreshCache()

    // Get locations from cache directly instead of using loadLocations
    // to avoid potential event publishing loops
    const cachedLocations = locationCache.getAllLocations()

    // Update state only if the locations have actually changed
    setLocations(prevLocations => {
      if (JSON.stringify(cachedLocations) !== JSON.stringify(prevLocations)) {
        return cachedLocations
      }
      return prevLocations
    })

    // Only update if needed
    setIsHomeServiceEnabled(prev => prev ? prev : true)
  }, []) // Remove dependencies to prevent excessive calls

  // Helper function to check if a location is a Home Service location
  const isHomeServiceLocation = useCallback((location: Location): boolean => {
    return location.id === "home" || location.name === "Home Service"
  }, [])

  return (
    <LocationContext.Provider
      value={{
        locations,
        getLocationById,
        getLocationName,
        getLocationIds,
        getActiveLocations,
        refreshLocations,
        isHomeServiceEnabled,
        addLocation,
        updateLocation,
        deleteLocation,
        syncLocations,
      }}
    >
      {children}
    </LocationContext.Provider>
  )
}

export const useLocations = () => useContext(LocationContext)
